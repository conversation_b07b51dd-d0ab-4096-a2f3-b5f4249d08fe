<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import { ArrowRight } from 'lucide-svelte';
  import { urlFor } from '$lib/sanity/sanityClient';
  import type { Resource } from '$lib/sanity/types';
  import { FileText, Briefcase, Calculator, Search } from 'lucide-svelte';

  export let resource: Resource;

  // Map icon string to component
  function getIconComponent(iconName: string) {
    switch (iconName) {
      case 'FileText':
        return FileText;
      case 'Briefcase':
        return Briefcase;
      case 'Calculator':
        return Calculator;
      case 'Search':
        return Search;
      default:
        return FileText;
    }
  }

  const IconComponent = resource.icon ? getIconComponent(resource.icon) : FileText;
</script>

<Card.Root class="flex h-full flex-col overflow-hidden">
  {#if resource.mainImage}
    <div class="h-48 w-full overflow-hidden bg-gray-100">
      <img
        src={urlFor(resource.mainImage, { width: 400, height: 200, fit: 'crop' })}
        alt={resource.title}
        class="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
        on:error={(e) => {
          const img = e.currentTarget as HTMLImageElement;
          img.src =
            'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPlJlc291cmNlPC90ZXh0Pjwvc3ZnPg==';
        }} />
    </div>
  {/if}

  <Card.Header class="flex items-center gap-3 p-4">
    <div class="bg-primary/10 text-primary flex h-8 w-8 items-center justify-center rounded-full">
      <svelte:component this={IconComponent} class="h-4 w-4" />
    </div>
    <div>
      <Card.Title>{resource.title}</Card.Title>
      <Card.Description class="text-xs">{resource.category}</Card.Description>
    </div>
  </Card.Header>

  <Card.Content class="flex-1 p-4 pt-0">
    <p class="text-gray-600">{resource.description}</p>
  </Card.Content>

  <Card.Footer class="p-4">
    <a
      href={`/resources/${resource.slug.current}`}
      class="text-primary inline-flex items-center text-sm font-medium hover:underline">
      View Resource
      <ArrowRight class="ml-1 h-3 w-3" />
    </a>
  </Card.Footer>
</Card.Root>
