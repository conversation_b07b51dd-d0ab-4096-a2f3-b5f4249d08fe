<script lang="ts">
  import { urlFor } from '$lib/sanity/sanityClient';

  export let block: any;
</script>

<figure class="my-8">
  <img
    src={urlFor(block, { width: 800 })}
    alt={block.alt || ''}
    class="w-full rounded-lg"
    on:error={(e) => {
      e.currentTarget.src =
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzZiNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlPC90ZXh0Pjwvc3ZnPg==';
    }} />
  {#if block.caption}
    <figcaption class="mt-2 text-center text-sm text-gray-500">{block.caption}</figcaption>
  {/if}
</figure>
